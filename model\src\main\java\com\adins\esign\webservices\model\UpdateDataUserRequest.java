package com.adins.esign.webservices.model;

import com.adins.framework.service.base.model.MssRequestType;

public class UpdateDataUserRequest extends MssRequestType {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String loginId;
	private String noPhone;
	private String newEmail;
	private String isActive;
	private String isRegist;
	private String vendorCode;
	private String ipAddress;
	
	public String getNoPhone() {
		return noPhone;
	}
	public void setNoPhone(String noPhone) {
		this.noPhone = noPhone;
	}
	public String getNewEmail() {
		return newEmail;
	}
	public void setNewEmail(String newEmail) {
		this.newEmail = newEmail;
	}
	public String getIsActive() {
		return isActive;
	}
	public void setIsActive(String isActive) {
		this.isActive = isActive;
	}
	public String getIsRegist() {
		return isRegist;
	}
	public void setIsRegist(String isRegist) {
		this.isRegist = isRegist;
	}
	public String getLoginId() {
		return loginId;
	}
	public void setLoginId(String loginId) {
		this.loginId = loginId;
	}
	public String getVendorCode() {
		return vendorCode;
	}
	public void setVendorCode(String vendorCode) {
		this.vendorCode = vendorCode;
	}
	public String getIpAddress() {
		return ipAddress;
	}
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

}
